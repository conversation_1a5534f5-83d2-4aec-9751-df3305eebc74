// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_state_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$deviceStateNotifierHash() =>
    r'7e50e0dd8f1f7c5d24cf6764c746e6b5c4e24285';

/// See also [DeviceStateNotifier].
@ProviderFor(DeviceStateNotifier)
final deviceStateNotifierProvider =
    NotifierProvider<DeviceStateNotifier, DeviceState>.internal(
  DeviceStateNotifier.new,
  name: r'deviceStateNotifierProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$deviceStateNotifierHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$DeviceStateNotifier = Notifier<DeviceState>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
