// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_assistant_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$homeAssistantServiceHash() =>
    r'5b17def03dd284e3eb7bca40e39e8633e5374611';

/// See also [homeAssistantService].
@ProviderFor(homeAssistantService)
final homeAssistantServiceProvider = Provider<HomeAssistantService>.internal(
  homeAssistantService,
  name: r'homeAssistantServiceProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$homeAssistantServiceHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef HomeAssistantServiceRef = ProviderRef<HomeAssistantService>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
