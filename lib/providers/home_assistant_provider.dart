import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../services/home_assistant_service.dart';

part 'home_assistant_provider.g.dart';

@Riverpod(keepAlive: true)
HomeAssistantService homeAssistantService(HomeAssistantServiceRef ref) {
  final service = HomeAssistantService();
  
  // Connect to Home Assistant when the provider is created
  service.connect();
  
  // Dispose of the service when the provider is disposed
  ref.onDispose(() {
    service.dispose();
  });
  
  return service;
}
