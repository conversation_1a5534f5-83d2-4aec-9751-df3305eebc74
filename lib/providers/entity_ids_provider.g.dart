// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'entity_ids_provider.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$entityIdsHash() => r'522d08208bb21e4ec77ce70453f414c770859ebd';

/// See also [entityIds].
@ProviderFor(entityIds)
final entityIdsProvider = Provider<EntityIds>.internal(
  entityIds,
  name: r'entityIdsProvider',
  debugGetCreateSourceHash:
      const bool.fromEnvironment('dart.vm.product') ? null : _$entityIdsHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
typedef EntityIdsRef = ProviderRef<EntityIds>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
