import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:convert';
import 'dart:developer';
import 'package:web_socket_channel/web_socket_channel.dart';

class DeviceType {
  static const String fan = 'fan';
  static const String light = 'light';
  static const String power = 'power';
  static const String favourite = 'favourite';
}

class VirtualDock extends StatefulWidget {
  const VirtualDock({super.key});

  @override
  State<VirtualDock> createState() => _VirtualDockState();
}

class _VirtualDockState extends State<VirtualDock> {
  String selectedDevice = DeviceType.fan;
  double temperature = 20.0;
  bool isFanOn = false;
  int fanLevel = 1; // Default to level 1 (valid range is 0-4)
  String fanMode = 'Normal';
  bool isLightOn = false;
  double brightness = 0.5;
  bool isPower1On = false;
  bool isPower2On = false;

  String power1entityId = "switch.on_off_plugin_unit_switch_1_3";
  String power2entityId = "switch.on_off_plugin_unit_switch_2_3";
  String lightentityId = "light.dimmer_light";
  String fanentityId = "fan.fan";
  String favourite1entityId = "light.wall_dock_light_6";
  String favourite2entityId = "light.wall_dock_light_5";

  // Favorite lights state
  bool isFavourite1On = false; // State for favourite1entityId
  bool isFavourite2On = false; // State for favourite2entityId

  // WebSocket variables
  late WebSocketChannel channel;
  bool isConnected = false;
  int messageId = 1;

  // Fan modes
  //final List<String> fanModes = ['Low', 'Medium', 'High', 'Auto'];

  // Helper method to ensure fan level is within valid range
  int _validateFanLevel(int level) {
    if (level < 0) {
      log('WARNING: Fan level $level is below minimum of 0, setting to 0');
      return 0;
    } else if (level > 4) {
      log('WARNING: Fan level $level exceeds maximum of 4, capping at 4');
      return 4;
    }
    return level;
  }

  @override
  void initState() {
    super.initState();
    // Ensure initial fan level is valid
    fanLevel = _validateFanLevel(fanLevel);
    connectWebSocket();
  }

  @override
  void dispose() {
    if (isConnected) {
      channel.sink.close();
    }
    super.dispose();
  }

  void connectWebSocket() {
    try {
      channel = WebSocketChannel.connect(
        Uri.parse('ws://homeassistant.local:8123/api/websocket'),
      );

      channel.sink.add(jsonEncode({
        "type": "auth",
        "access_token":
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY"
      }));

      // Function to subscribe to events after authentication
      void subscribeToEvents() {
        // Subscribe to state changes
        final subscribeMessage = {
          "id": messageId++,
          "type": "subscribe_events",
          "event_type": "state_changed"
        };
        sendMessage(subscribeMessage);
      }

      channel.stream.listen(
        (message) {
          log('Received: $message');
          final data = jsonDecode(message);

          // Handle state updates from Home Assistant
          if (data['type'] == 'event' && data['event'] != null) {
            final event = data['event'];
            if (event['event_type'] == 'state_changed') {
              final entityId = event['data']['entity_id'];
              final newState = event['data']['new_state']['state'];

              setState(() {
                // Update fan state
                if (entityId == fanentityId) {
                  isFanOn = newState == 'on';
                  if (event['data']['new_state']['attributes'] != null &&
                      event['data']['new_state']['attributes']['percentage'] != null) {
                    final percentage = event['data']['new_state']['attributes']['percentage'];
                    log('Received fan percentage from device: $percentage');

                    // Get raw percentage value for debugging
                    int rawPercentage = percentage.toInt();

                    // Calculate what the physical device might be sending
                    int physicalLevel = ((rawPercentage + 19) / 20).floor(); // This would map 1-100% to levels 1-5
                    log('Physical device likely using level: $physicalLevel (from $rawPercentage%)');

                    // Map percentage ranges to specific levels (0-4)
                    int newFanLevel;
                    if (rawPercentage <= 0) {
                      newFanLevel = 0; // Off
                    } else if (rawPercentage <= 25) {
                      newFanLevel = 1; // Level 1
                    } else if (rawPercentage <= 50) {
                      newFanLevel = 2; // Level 2
                    } else if (rawPercentage <= 75) {
                      newFanLevel = 3; // Level 3
                    } else {
                      newFanLevel = 4; // Level 4 (max)
                    }

                    // Validate the fan level
                    newFanLevel = _validateFanLevel(newFanLevel);

                    log('Mapped to fan level: $newFanLevel (previous: $fanLevel)');
                    fanLevel = newFanLevel;
                  }
                }

                // Update light state
                else if (entityId == lightentityId) {
                  isLightOn = newState == 'on';
                  if (isLightOn && event['data']['new_state']['attributes'] != null &&
                      event['data']['new_state']['attributes']['brightness'] != null) {
                    final rawBrightness = event['data']['new_state']['attributes']['brightness'];
                    brightness = rawBrightness / 255.0; // Convert 0-255 to 0.0-1.0
                  }
                }

                // Update power 1 state
                else if (entityId == power1entityId) {
                  isPower1On = newState == 'on';
                }

                // Update power 2 state
                else if (entityId == power2entityId) {
                  isPower2On = newState == 'on';
                }

                // Update favorite 1 light state
                else if (entityId == favourite1entityId) {
                  isFavourite1On = newState == 'on';
                  log('Favorite 1 light state updated: ${isFavourite1On ? 'ON' : 'OFF'}');
                }

                // Update favorite 2 light state
                else if (entityId == favourite2entityId) {
                  isFavourite2On = newState == 'on';
                  log('Favorite 2 light state updated: ${isFavourite2On ? 'ON' : 'OFF'}');
                }
              });
            }
          }

          // Handle authentication success and subscribe to events
          if (data['type'] == 'auth_ok') {
            setState(() {
              isConnected = true;
            });
            subscribeToEvents();
          } else {
            setState(() {
              isConnected = true;
            });
          }
        },
        onError: (error) {
          log('WebSocket Error: $error');
          setState(() {
            isConnected = false;
          });
        },
        onDone: () {
          log('WebSocket Connection Closed');
          setState(() {
            isConnected = false;
          });
        },
      );
    } catch (e) {
      log('Error connecting to WebSocket: $e');
    }
  }

  void sendMessage(Map<String, dynamic> message) {
    if (isConnected) {
      try {
        channel.sink.add(jsonEncode(message));
        log('Sent message: ${jsonEncode(message)}');
      } catch (e) {
        log('Error sending message: $e');
        connectWebSocket();
      }
    } else {
      log('WebSocket not connected. Attempting to reconnect...');
      connectWebSocket();
    }
  }

  // Fan control methods
  void toggleFan() {
    // Ensure fanLevel is within valid range
    fanLevel = _validateFanLevel(fanLevel);

    // If turning on and level is 0, set to minimum level 1
    if (fanLevel < 1 && !isFanOn) {
      log('Setting fan to minimum level 1 when turning on');
      fanLevel = 1;
    }

    setState(() {
      isFanOn = !isFanOn;
    });

    if (isFanOn) {
      // Map fan levels 1-4 to specific percentages
      int percentage;
      switch (fanLevel) {
        case 1:
          percentage = 25;
          break;
        case 2:
          percentage = 50;
          break;
        case 3:
          percentage = 75;
          break;
        case 4:
          percentage = 100;
          break;
        default:
          percentage = 25; // Default to lowest speed if invalid level
      }

      log('Toggle fan ON - level: $fanLevel, percentage: $percentage');

      // First set the percentage
      final percentageMessage = {
        "type": "call_service",
        "domain": "fan",
        "service": "set_percentage",
        "return_response": false,
        "service_data": {
          "entity_id": fanentityId,
          "percentage": percentage
        },
        "id": messageId++
      };
      sendMessage(percentageMessage);

      // Then turn on the fan
      final turnOnMessage = {
        "type": "call_service",
        "domain": "fan",
        "service": "turn_on",
        "return_response": false,
        "service_data": {
          "entity_id": fanentityId
        },
        "id": messageId++
      };
      sendMessage(turnOnMessage);
    } else {
      log('Toggle fan OFF');
      // Turn off the fan
      final message = {
        "type": "call_service",
        "domain": "fan",
        "service": "turn_off",
        "return_response": false,
        "service_data": {
          "entity_id": fanentityId
        },
        "id": messageId++
      };
      sendMessage(message);
    }
  }

  void updateFanSpeed(int level) {
    // Ensure level is within valid range
    level = _validateFanLevel(level);

    log('Setting fan speed to level: $level');

    setState(() {
      fanLevel = level;
      isFanOn = level > 0; // Set fan on only if level > 0
    });

    if (level == 0) {
      // Turn off the fan if level is 0
      final message = {
        "type": "call_service",
        "domain": "fan",
        "service": "turn_off",
        "return_response": false,
        "service_data": {
          "entity_id": fanentityId
        },
        "id": messageId++
      };
      sendMessage(message);
    } else {
      // Map fan levels 1-4 to specific percentages
      int percentage;
      switch (level) {
        case 1:
          percentage = 25;
          break;
        case 2:
          percentage = 50;
          break;
        case 3:
          percentage = 75;
          break;
        case 4:
          percentage = 100;
          break;
        default:
          percentage = 25; // Default to lowest speed if invalid level
      }

      log('Converting level $level to percentage: $percentage');

      // Set the fan percentage for levels 1-4
      final message = {
        "type": "call_service",
        "domain": "fan",
        "service": "set_percentage",
        "return_response": false,
        "service_data": {
          "entity_id": fanentityId,
          "percentage": percentage
        },
        "id": messageId++
      };
      sendMessage(message);
    }
  }

  // Light control methods
  void toggleLight() {
    final service = isLightOn ? "turn_off" : "turn_on";

    setState(() {
      isLightOn = !isLightOn;
    });

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": service,
      "return_response": false,
      "service_data": {
        "entity_id": lightentityId
      },
      "id": messageId++
    };
    sendMessage(message);
  }

  void updateBrightness(double value) {
    setState(() {
      brightness = value;
      isLightOn = true;
    });

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": "turn_on",
      "return_response": false,
      "service_data": {
        "entity_id": lightentityId,
        "brightness_pct": (value * 100).round()
      },
      "id": messageId++
    };
    sendMessage(message);
  }

  // Power control methods
  void togglePower1() {
    final service = isPower1On ? "turn_off" : "turn_on";

    setState(() {
      isPower1On = !isPower1On;
    });

    final message = {
      "type": "call_service",
      "domain": "switch",
      "service": service,
      "return_response": false,
      "service_data": {
        "entity_id": power1entityId
      },
      "id": messageId++
    };
    sendMessage(message);
  }

  void togglePower2() {
    final service = isPower2On ? "turn_off" : "turn_on";

    setState(() {
      isPower2On = !isPower2On;
    });

    final message = {
      "type": "call_service",
      "domain": "switch",
      "service": service,
      "return_response": false,
      "service_data": {
        "entity_id": power2entityId
      },
      "id": messageId++
    };
    sendMessage(message);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Bedroom > Wall Dock - 1'),
        actions: [
          IconButton(
            icon: Icon(isConnected ? Icons.wifi : Icons.wifi_off),
            onPressed: connectWebSocket,
          ),
        ],
      ),
      backgroundColor: Colors.grey[900],
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Combined device control panel with dynamic circular widget
                      _buildCombinedControlPanel(constraints.maxWidth),
                      // Device selection grid fixed at bottom
                      _buildDeviceGrid(),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // Fan circular widget
  Widget _buildFanCircularWidget(double arcSize, double centerCircleSize, double fontSize) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Fan speed arc
        CustomPaint(
          size: Size(arcSize, arcSize),
          painter: FanSpeedArcPainter(_validateFanLevel(fanLevel) / 4), // Normalize to 0.0-1.0
        ),

        // Center fan level display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: isFanOn ? Colors.blue : Colors.grey[700],
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                isFanOn ? '${_validateFanLevel(fanLevel)}' : 'OFF',
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Light circular widget
  Widget _buildLightCircularWidget(double arcSize, double centerCircleSize, double fontSize) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Brightness arc
        CustomPaint(
          size: Size(arcSize, arcSize),
          painter: BrightnessArcPainter(brightness), // Already 0.0-1.0
        ),

        // Center brightness display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: isLightOn ? Colors.amber.withAlpha((brightness * 255).round()) : Colors.grey[700],
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                isLightOn ? '${(brightness * 100).round()}%' : 'OFF',
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Power circular widget
  Widget _buildPowerCircularWidget(double arcSize, double centerCircleSize, double fontSize, bool isPowerOn) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Power status arc
        CustomPaint(
          size: Size(arcSize, arcSize),
          painter: PowerArcPainter(isPowerOn ? 1.0 : 0.0),
        ),
        // Center power status display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: isPowerOn ? Colors.green : Colors.red,
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                isPowerOn ? Icons.power : Icons.power_off,
                color: Colors.white,
                size: fontSize,
              ),
              Text(
                isPowerOn ? 'ON' : 'OFF',
                style: TextStyle(
                  fontSize: fontSize * 0.5,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Favourite circular widget
  Widget _buildFavouriteCircularWidget(double arcSize, double centerCircleSize, double fontSize, int favouriteNumber) {
    return Stack(
      alignment: Alignment.center,
      children: [
        // Favourite icon display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: Colors.blue,
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.favorite,
                color: Colors.yellow,
                size: fontSize,
              ),
              Text(
                'Scene $favouriteNumber',
                style: TextStyle(
                  fontSize: fontSize * 0.4,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDeviceGrid() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine if we should use a grid or column layout based on width
        final useGrid = constraints.maxWidth > 500;

        if (useGrid) {
          // Grid layout for wider screens
          return GridView.count(
            shrinkWrap: true,
            physics: NeverScrollableScrollPhysics(),
            crossAxisCount: 3,
            childAspectRatio: 1.2,
            children: [
              _buildDeviceButton(DeviceType.fan, Icons.wind_power_rounded, 'Fan'),
              _buildDeviceButton(DeviceType.light, Icons.lightbulb, 'Light'),
              _buildDeviceButton(DeviceType.power, Icons.power, 'Power 1'),
              _buildDeviceButton(DeviceType.power, Icons.power, 'Power 2', isSecondPower: true),
              _buildDeviceButton(DeviceType.favourite, Icons.favorite, 'Scene - OFF', isFav1: true),
              _buildDeviceButton(DeviceType.favourite, Icons.favorite, 'Scene - ON', isFav2: true),
            ],
          );
        } else {
          // Column layout for narrower screens
          return SizedBox(
            height: 240, // Fixed height for the grid
            child: Column(
              children: [
                // First row of devices
                Expanded(
                  child: Row(
                    children: [
                      _buildDeviceButton(DeviceType.fan, Icons.wind_power_rounded, 'Fan'),
                      _buildDeviceButton(DeviceType.light, Icons.lightbulb, 'Light'),
                      _buildDeviceButton(DeviceType.power, Icons.power, 'Power 1'),
                    ],
                  ),
                ),
                // Second row of devices
                Expanded(
                  child: Row(
                    children: [
                      _buildDeviceButton(DeviceType.power, Icons.power, 'Power 2', isSecondPower: true),
                      _buildDeviceButton(DeviceType.favourite, Icons.favorite, 'Scene - OFF', isFav1: true),
                      _buildDeviceButton(DeviceType.favourite, Icons.favorite, 'Scene - ON', isFav2: true),
                    ],
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  Widget _buildDeviceButton(String type, IconData icon, String label, {bool isSecondPower = false, bool isFav1 = false, bool isFav2 = false}) {
    bool isSelected = false;

    if (type == DeviceType.fan && selectedDevice == DeviceType.fan) {
      isSelected = true;
    } else if (type == DeviceType.light && selectedDevice == DeviceType.light) {
      isSelected = true;
    } else if (type == DeviceType.power && selectedDevice == DeviceType.power && !isSecondPower) {
      isSelected = true;
    } else if (type == DeviceType.power && selectedDevice == 'power2' && isSecondPower) {
      isSelected = true;
    } else if (type == DeviceType.favourite && selectedDevice == 'favourite1' && isFav1) {
      isSelected = true;
    } else if (type == DeviceType.favourite && selectedDevice == 'favourite2' && isFav2) {
      isSelected = true;
    }

    return Expanded(
      child: LayoutBuilder(
        builder: (context, constraints) {
          // Fixed sizes for all device cards
          final iconSize = 28.0;
          final fontSize = 14.0;
          final padding = 8.0;

          return Padding(
            padding: EdgeInsets.all(padding),
            child: InkWell(
              onTap: () {
                setState(() {
                  if (type == DeviceType.power && isSecondPower) {
                    selectedDevice = 'power2';
                  } else if (type == DeviceType.favourite && isFav1) {
                    selectedDevice = 'favourite1';
                    // Execute favourite1 action
                    _executeFavouriteAction(1);
                  } else if (type == DeviceType.favourite && isFav2) {
                    selectedDevice = 'favourite2';
                    // Execute favourite2 action
                    _executeFavouriteAction(2);
                  } else {
                    selectedDevice = type;
                  }
                });
              },
              child: Container(height: 180,
                decoration: BoxDecoration(
                  color: isSelected ? const Color.fromARGB(255, 74, 106, 136).withAlpha(180) : Colors.grey[800],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: 4.0),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: iconSize,
                      ),
                    ),
                    SizedBox(height: padding / 2),
                    Text(
                      label,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: fontSize,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (type != DeviceType.favourite)
                      Transform.scale(
                        scale: 0.8,
                        child: Switch(
                          value: _getDeviceState(type, isSecondPower),
                          onChanged: (value) => _toggleDevice(type, isSecondPower),
                          activeColor: Colors.blue,
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  bool _getDeviceState(String type, bool isSecondPower) {
    if (type == DeviceType.fan) return isFanOn;
    if (type == DeviceType.light) return isLightOn;
    if (type == DeviceType.power && !isSecondPower) return isPower1On;
    if (type == DeviceType.power && isSecondPower) return isPower2On;
    return false;
  }

  void _toggleDevice(String type, bool isSecondPower) {
    if (type == DeviceType.fan) {
      toggleFan();
    } else if (type == DeviceType.light) {
      toggleLight();
    } else if (type == DeviceType.power && !isSecondPower) {
      togglePower1();
    } else if (type == DeviceType.power && isSecondPower) {
      togglePower2();
    }
  }

  void _executeFavouriteAction(int favouriteNumber) {
    // Determine entity ID and current state based on favourite number
    String entityId;
    bool isCurrentlyOn;

    if (favouriteNumber == 1) {
      // First favorite button is for OFF action
      entityId = favourite1entityId;
      isCurrentlyOn = isFavourite1On;
    } else {
      // Second favorite button is for ON action
      entityId = favourite2entityId;
      isCurrentlyOn = isFavourite2On;
    }

    // Toggle the state (turn off if on, turn on if off)
    String service = isCurrentlyOn ? "turn_off" : "turn_on";

    debugPrint('Executing Favourite $favouriteNumber action: $service for $entityId');
    debugPrint('Current state: ${isCurrentlyOn ? "ON" : "OFF"}');

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": service,
      "return_response": false,
      "service_data": {
        "entity_id": entityId
      },
      "id": messageId++
    };
    sendMessage(message);
  }

  Widget _buildCombinedControlPanel(double maxWidth) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate responsive sizes for the circular widget
        final containerHeight = maxWidth < 400 ? 250.0 : 250.0;
        final arcSize = maxWidth < 400 ? 220.0 : 220.0;
        final centerCircleSize = maxWidth < 400 ? 120.0 : 120.0;
        final fontSize = maxWidth < 400 ? 32.0 : 32.0;

        // Calculate responsive sizes for the container

        return Container(
          decoration: BoxDecoration(
            color: Colors.grey[850],
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            children: [
              // Dynamic circular widget
              Container(
                height: containerHeight,
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // Arc background and progress
                    _buildDynamicCircularWidget(arcSize, centerCircleSize, fontSize),
                  ],
                ),
              ),

              // Device specific controls
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: _buildDeviceSpecificControls(maxWidth),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDeviceSpecificControls(double maxWidth) {
    switch (selectedDevice) {
      case DeviceType.fan:
        return _buildFanControls(maxWidth);
      case DeviceType.light:
        return _buildLightControls(maxWidth);
      case DeviceType.power:
        return _buildPowerControls(isPower1On, (value) {
          setState(() => isPower1On = value);
        }, maxWidth);
      case 'power2':
        return _buildPowerControls(isPower2On, (value) {
          setState(() => isPower2On = value);
        }, maxWidth);
      case 'favourite1':
        return _buildFavouriteControls(1, maxWidth);
      case 'favourite2':
        return _buildFavouriteControls(2, maxWidth);
      default:
        return Container();
    }
  }

  Widget _buildDynamicCircularWidget(double arcSize, double centerCircleSize, double fontSize) {
    switch (selectedDevice) {
      case DeviceType.fan:
        return _buildFanCircularWidget(arcSize, centerCircleSize, fontSize);
      case DeviceType.light:
        return _buildLightCircularWidget(arcSize, centerCircleSize, fontSize);
      case DeviceType.power:
        return _buildPowerCircularWidget(arcSize, centerCircleSize, fontSize, isPower1On);
      case 'power2':
        return _buildPowerCircularWidget(arcSize, centerCircleSize, fontSize, isPower2On);
      case 'favourite1':
        return _buildFavouriteCircularWidget(arcSize, centerCircleSize, fontSize, 1);
      case 'favourite2':
        return _buildFavouriteCircularWidget(arcSize, centerCircleSize, fontSize, 2);
      default:
        return Container();
    }
  }

  Widget _buildFanControls(double maxWidth) {
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = maxWidth < 400 ? 4.0 : 8.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Fan Control',
                  style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: isFanOn,
                  onChanged: (value) {
                    toggleFan();
                  },
                  activeColor: Colors.blue,
                ),
              ],
            ),
            SizedBox(height: verticalSpacing / 2),
            const Text('Fan Level'),
            Slider(
              value: fanLevel > 4 ? 4.0 : fanLevel.toDouble(), // Ensure value doesn't exceed max
              min: 0,
              max: 4,
              divisions: 4,
              label: 'Level ${fanLevel > 4 ? 4 : fanLevel}',
              onChanged: (value) {
                updateFanSpeed(value.toInt());
              },
            ),
            // if (maxWidth >= 360) ... [
            //   const Text('Fan Mode'),
            //   const SizedBox(height: 2),
            //   Row(
            //     mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            //     children: fanModes.map((mode) {
            //       return ChoiceChip(
            //         label: Text(mode, style: TextStyle(fontSize: maxWidth < 400 ? 10 : 12)),
            //         selected: fanMode == mode,
            //         labelPadding: const EdgeInsets.symmetric(horizontal: 4),
            //         padding: const EdgeInsets.all(2),
            //         onSelected: isFanOn
            //             ? (selected) {
            //                 if (selected) {
            //                   setState(() => fanMode = mode);
            //                 }
            //               }
            //             : null,
            //       );
            //     }).toList(),
            //   ),
            // ],
          ],
        ),
      ),
    );
  }

  Widget _buildLightControls(double maxWidth) {
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = maxWidth < 400 ? 4.0 : 8.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Light Control',
                  style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
                ),
                Switch(
                  value: isLightOn,
                  onChanged: (value) {
                    toggleLight();
                  },
                  activeColor: Colors.blue,
                ),
              ],
            ),
            SizedBox(height: verticalSpacing / 2),
            const Text('Brightness'),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: isLightOn ? Colors.amber : Colors.grey[600],
                inactiveTrackColor: isLightOn ? Colors.amber.withValues(alpha: 77) : Colors.grey[800],
                thumbColor: isLightOn ? Colors.amber : Colors.grey[400],
                overlayColor: isLightOn ? Colors.amber.withValues(alpha: 77) : Colors.grey.withValues(alpha: 77),
              ),
              child: Slider(
                value: brightness,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                label: '${(brightness * 100).round()}%',
                onChanged: (value) {
                  updateBrightness(value);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPowerControls(bool isPowerOn, Function(bool) onChanged, double maxWidth) {
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Power Control',
              style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
            ),
            Switch(
              value: isPowerOn,
              onChanged: (value) {
                if (selectedDevice == DeviceType.power) {
                  togglePower1();
                } else if (selectedDevice == 'power2') {
                  togglePower2();
                }
              },
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFavouriteControls(int favouriteNumber, double maxWidth) {
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;
    final verticalSpacing = maxWidth < 400 ? 4.0 : 8.0;
    final buttonPadding = maxWidth < 400
        ? const EdgeInsets.symmetric(horizontal: 16, vertical: 8)
        : const EdgeInsets.symmetric(horizontal: 24, vertical: 12);

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Scene $favouriteNumber',
              style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: verticalSpacing),
            Center(
              child: ElevatedButton(
                onPressed: () => _executeFavouriteAction(favouriteNumber),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  padding: buttonPadding,
                ),
                child: const Text('Execute Action'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Fan Speed Arc Painter
class FanSpeedArcPainter extends CustomPainter {
  final double progress; // 0.0 to 1.0

  FanSpeedArcPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;

    // Background arc
    final bgPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      math.pi * 1.4, // Sweep angle
      false,
      bgPaint,
    );

    // Progress arc
    final progressAngle = math.pi * 1.4 * progress;
    final progressPaint = Paint()
      ..color = Colors.blue
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      progressAngle, // Sweep angle based on progress
      false,
      progressPaint,
    );

    // Draw the indicator dot
    final dotAngle = math.pi * 0.8 + progressAngle;
    final dotX = center.dx + radius * math.cos(dotAngle);
    final dotY = center.dy + radius * math.sin(dotAngle);

    final dotPaint = Paint()..color = Colors.blue;
    canvas.drawCircle(Offset(dotX, dotY), 5, dotPaint);
  }

  @override
  bool shouldRepaint(covariant FanSpeedArcPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

// Brightness Arc Painter
class BrightnessArcPainter extends CustomPainter {
  final double progress; // 0.0 to 1.0

  BrightnessArcPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;

    // Background arc
    final bgPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      math.pi * 1.4, // Sweep angle
      false,
      bgPaint,
    );

    // Progress arc
    final progressAngle = math.pi * 1.4 * progress;
    final progressPaint = Paint()
      ..color = Colors.amber
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      progressAngle, // Sweep angle based on progress
      false,
      progressPaint,
    );

    // Draw the indicator dot
    final dotAngle = math.pi * 0.8 + progressAngle;
    final dotX = center.dx + radius * math.cos(dotAngle);
    final dotY = center.dy + radius * math.sin(dotAngle);

    final dotPaint = Paint()..color = Colors.amber;
    canvas.drawCircle(Offset(dotX, dotY), 5, dotPaint);
  }

  @override
  bool shouldRepaint(covariant BrightnessArcPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}

// Power Arc Painter
class PowerArcPainter extends CustomPainter {
  final double progress; // 0.0 or 1.0

  PowerArcPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = size.width / 2 - 10;

    // Background arc
    final bgPaint = Paint()
      ..color = Colors.grey[700]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 10
      ..strokeCap = StrokeCap.round;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi * 0.8, // Start angle
      math.pi * 1.4, // Sweep angle
      false,
      bgPaint,
    );

    if (progress > 0) {
      // Progress arc (full when on, none when off)
      final progressPaint = Paint()
        ..color = progress > 0 ? Colors.green : Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 10
        ..strokeCap = StrokeCap.round;

      canvas.drawArc(
        Rect.fromCircle(center: center, radius: radius),
        math.pi * 0.8, // Start angle
        math.pi * 1.4, // Full sweep when on
        false,
        progressPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant PowerArcPainter oldDelegate) {
    return oldDelegate.progress != progress;
  }
}