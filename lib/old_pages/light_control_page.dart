import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import 'package:web_socket_channel/web_socket_channel.dart';


class LightControlPage extends StatefulWidget {
  const LightControlPage({super.key});

  @override
  State<LightControlPage> createState() => _LightControlPageState();
}

class _LightControlPageState extends State<LightControlPage> {
  late WebSocketChannel channel;
  bool isConnected = false;
  bool isLightOn = false;
  double brightness = 0.03;
  Color currentColor = Colors.green;
  int messageId = 1;
  double colorTemperature = 4000;
  String selectedEffect = 'None';
  List<String> lightEffects = ['None', 'Rainbow', 'Pulse', 'Breathe'];

  final List<Color> predefinedColors = [
    const Color.fromRGBO(255, 140, 0, 1),
    const Color.fromRGBO(255, 0, 0, 1),
    const Color.fromRGBO(255, 180, 140, 1),
    const Color.fromRGBO(255, 220, 180, 1),
    const Color.fromRGBO(255, 255, 255, 1),
    const Color.fromRGBO(0, 0, 255, 1),
    const Color.fromRGBO(128, 0, 128, 1),
    const Color.fromRGBO(255, 192, 203, 1),
    const Color.fromRGBO(13, 197, 43, 1),
    const Color.fromRGBO(248, 191, 3, 1),
    const Color.fromRGBO(17, 130, 182, 1),
    const Color.fromRGBO(0, 225, 255, 1),
    const Color.fromRGBO(123, 255, 0, 1),
    const Color.fromRGBO(255, 0, 98, 1)
  ];

  @override
  void initState() {
    super.initState();
    connectWebSocket();
  }

  void sendMessage(Map<String, dynamic> message) {
    if (isConnected) {
      try {
        channel.sink.add(jsonEncode(message));
        log('Sent message: ${jsonEncode(message)}');
      } catch (e) {
        log('Error sending message: $e');
        connectWebSocket();
      }
    } else {
      log('WebSocket not connected. Attempting to reconnect...');
      connectWebSocket();
    }
  }

  void connectWebSocket() {
    try {
      channel = WebSocketChannel.connect(
        Uri.parse('ws://homeassistant.local:8123/api/websocket'),
      );

      channel.sink.add(jsonEncode({
        "type": "auth",
        "access_token":
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiI5ZjVlM2QyNjQ4ZjQ0NDAwOWE0NWExNjJlMDAwZmRjZSIsImlhdCI6MTczODgxODgwNSwiZXhwIjoyMDU0MTc4ODA1fQ.vOEYd3rL0poOHWUOkf0_QTwxpkzDTtubCzpvlb9zWcY"
      }));

      channel.stream.listen(
        (message) {
          log('Received: $message');
          setState(() {
            isConnected = true;
          });
        },
        onError: (error) {
          log('WebSocket Error: $error');
          setState(() {
            isConnected = false;
          });
        },
        onDone: () {
          log('WebSocket Connection Closed');
          setState(() {
            isConnected = false;
          });
        },
      );
    } catch (e) {
      log('Error connecting to WebSocket: $e');
    }
  }

  void toggleLight() {
    final message = {
      "type": "call_service",
      "domain": "light",
      "service": isLightOn ? "turn_off" : "turn_on",
      "return_response": false,
      "service_data": {"entity_id": "light.test_product_3"},
      "id": messageId++
    };

    sendMessage(message);
    setState(() {
      isLightOn = !isLightOn;
    });
  }

  void updateBrightness(double value) {
    setState(() {
      isLightOn = true;
      brightness = value;
    });

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": "turn_on",
      "return_response": false,
      "service_data": {
        "entity_id": "light.test_product_3",
        "brightness_pct": (value * 100).round()
      },
      "id": messageId++
    };

    sendMessage(message);
  }

  void updateColor(Color color) {
    setState(() {
      isLightOn = true;
      currentColor = color;
    });

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": "turn_on",
      "return_response": false,
      "service_data": {
        "entity_id": "light.test_product_3",
        "rgb_color": [color.red, color.green, color.blue]
      },
      "id": messageId++
    };

    log('Sending color: RGB(${color.red}, ${color.green}, ${color.blue})');
    sendMessage(message);
  }

  void updateColorTemperature(double temperature) {
    setState(() {
      colorTemperature = temperature;
    });

    final message = {
      "type": "call_service",
      "domain": "light",
      "service": "turn_on",
      "return_response": false,
      "service_data": {
        "entity_id": "light.test_product_3",
        "color_temp": temperature.round()
      },
      "id": messageId++
    };

    sendMessage(message);
  }

  void showSettingsDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Light Settings'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  title: const Text('Effect'),
                  subtitle: DropdownButton<String>(
                    value: selectedEffect,
                    isExpanded: true,
                    items: lightEffects.map((String effect) {
                      return DropdownMenuItem<String>(
                        value: effect,
                        child: Text(effect),
                      );
                    }).toList(),
                    onChanged: (String? newValue) {
                      if (newValue != null) {
                        setState(() {
                          selectedEffect = newValue;
                        });
                        sendMessage({
                          "type": "call_service",
                          "domain": "light",
                          "service": "turn_on",
                          "service_data": {
                            "entity_id": "light.test_product_3",
                            "effect": newValue
                          },
                          "id": messageId++
                        });
                        Navigator.pop(context);
                      }
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Auto Turn Off'),
                  trailing: Switch(
                    value: false,
                    onChanged: (bool value) {},
                  ),
                ),
                ListTile(
                  title: const Text('Transition Duration'),
                  subtitle: Slider(
                    value: 1.0,
                    min: 0,
                    max: 10,
                    divisions: 10,
                    label: '1.0s',
                    onChanged: (double value) {},
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: const Text('Close'),
              onPressed: () => Navigator.pop(context),
            ),
          ],
        );
      },
    );
  }

  void showColorPicker() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Pick a Color'),
          content: SingleChildScrollView(
            child: ColorPicker(
              pickerColor: currentColor,
              onColorChanged: (Color color) {
                setState(() {
                  currentColor = color;
                });
              },
              pickerAreaHeightPercent: 0.8,
              enableAlpha: false,
              displayThumbColor: true,
              paletteType: PaletteType.hsvWithHue,
              portraitOnly: true,
            ),
          ),
          actions: [
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            TextButton(
              child: const Text('Apply'),
              onPressed: () {
                final selectedColor = Color.fromRGBO(currentColor.red,
                    currentColor.green, currentColor.blue, 1.0);
                updateColor(selectedColor);
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  void showColorTemperatureDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Color Temperature'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text('Warm                        Cool'),
              Slider(
                value: colorTemperature,
                min: 2000,
                max: 6500,
                divisions: 45,
                label: '${colorTemperature.round()}K',
                onChanged: (double value) {
                  setState(() {
                    colorTemperature = value;
                  });
                },
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _temperaturePresetButton('Candlelight', 2000),
                  _temperaturePresetButton('Warm', 2700),
                  _temperaturePresetButton('Neutral', 4000),
                  _temperaturePresetButton('Cool', 6500),
                ],
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.pop(context);
              },
            ),
            TextButton(
              child: const Text('Apply'),
              onPressed: () {
                sendMessage({
                  "type": "call_service",
                  "domain": "light",
                  "service": "turn_on",
                  "service_data": {
                    "entity_id": "light.test_product_3",
                    "color_temp": colorTemperature
                  },
                  "id": messageId++
                });
                Navigator.pop(context);
              },
            ),
          ],
        );
      },
    );
  }

  Widget _temperaturePresetButton(String label, double temp) {
    return InkWell(
      onTap: () {
        setState(() {
          colorTemperature = temp;
        });
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: _getColorTemperatureColor(temp),
              ),
            ),
            Text(label, style: const TextStyle(fontSize: 12)),
          ],
        ),
      ),
    );
  }

  Color _getColorTemperatureColor(double temp) {
    if (temp <= 2700) {
      return Colors.orange[300]!;
    } else if (temp <= 4000) {
      return Colors.yellow[100]!;
    } else {
      return Colors.blue[100]!;
    }
  }

  Widget buildControlButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        IconButton(
          icon: Icon(
            isLightOn
                ? Icons.power_settings_new
                : Icons.power_settings_new_outlined,
            color: isLightOn ? Colors.green : Colors.white,
          ),
          onPressed: toggleLight,
        ),
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: showSettingsDialog,
        ),
        IconButton(
          icon: const Icon(Icons.palette),
          onPressed: showColorPicker,
        ),
        IconButton(
          icon: const Icon(Icons.light_mode),
          onPressed: showColorTemperatureDialog,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: const Text('TEST_PRODUCT'),
        backgroundColor: Colors.black,
        actions: [
          IconButton(
            icon: Icon(isConnected ? Icons.wifi : Icons.wifi_off),
            onPressed: connectWebSocket,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              '${(brightness * 100).round()}%',
              style: const TextStyle(fontSize: 24),
            ),
            const Text(
              '8 seconds ago',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: currentColor.withOpacity(brightness),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      height: 4,
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // IconButton(
                //   icon: Icon(
                //     isLightOn
                //         ? Icons.power_settings_new
                //         : Icons.power_settings_new_outlined,
                //     color: isLightOn ? Colors.green : Colors.white,
                //   ),
                //   onPressed: toggleLight,
                // ),
                buildControlButtons(),
              ],
            ),
            const SizedBox(height: 20),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: predefinedColors.map((color) {
                return GestureDetector(
                  onTap: () => updateColor(color),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 20),
            SliderTheme(
              data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.white,
                inactiveTrackColor: Colors.grey[800],
                thumbColor: Colors.white,
                overlayColor: Colors.white.withValues(alpha: 0.3),
              ),
              child: Slider(
                value: brightness,
                onChanged: updateBrightness,
                min: 0.0,
                max: 1.0,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    channel.sink.close();
    super.dispose();
  }
}
