import 'package:freezed_annotation/freezed_annotation.dart';

part 'device_state.freezed.dart';
part 'device_state.g.dart';

@freezed
class DeviceState with _$DeviceState {
  const factory DeviceState({
    // Fan state
    @Default(false) bool isFanOn,
    @Default(1) int fanLevel,
    @Default('Normal') String fanMode,
    @Default(true) bool isFanOnline,

    // Light state
    @Default(false) bool isLightOn,
    @Default(0.5) double brightness,
    @Default(true) bool isLightOnline,

    // Power states
    @Default(false) bool isPower1On,
    @Default(false) bool isPower2On,
    @Default(true) bool isPower1Online,
    @Default(true) bool isPower2Online,

    // Favorite states
    @Default(false) bool isFavourite1On,
    @Default(false) bool isFavourite2On,
    @Default(true) bool isFavourite1Online,
    @Default(true) bool isFavourite2Online,

    // WebSocket connection state
    @Default(false) bool isConnected,
  }) = _DeviceState;

  factory DeviceState.fromJson(Map<String, dynamic> json) => _$DeviceStateFromJson(json);
}
