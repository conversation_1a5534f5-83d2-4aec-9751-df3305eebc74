/// Class to hold all entity IDs used in the application
class EntityIds {
  final String power1;
  final String power2;
  final String light;
  final String fan;
  final String favourite1;
  final String favourite2;

  const EntityIds({
    this.power1 = "switch.on_off_plugin_unit_switch_1_3",
    this.power2 = "switch.on_off_plugin_unit_switch_2_3",
    this.light = "light.dimmer_light",
    this.fan = "fan.fan",
    this.favourite1 = "light.wall_dock_light_6",
    this.favourite2 = "light.wall_dock_light_5",
  });

  // Default instance for easy access
  static const EntityIds defaultIds = EntityIds();
}
