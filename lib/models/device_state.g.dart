// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_state.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$DeviceStateImpl _$$DeviceStateImplFromJson(Map<String, dynamic> json) =>
    _$DeviceStateImpl(
      isFanOn: json['isFanOn'] as bool? ?? false,
      fanLevel: (json['fanLevel'] as num?)?.toInt() ?? 1,
      fanMode: json['fanMode'] as String? ?? 'Normal',
      isFanOnline: json['isFanOnline'] as bool? ?? true,
      isLightOn: json['isLightOn'] as bool? ?? false,
      brightness: (json['brightness'] as num?)?.toDouble() ?? 0.5,
      isLightOnline: json['isLightOnline'] as bool? ?? true,
      isPower1On: json['isPower1On'] as bool? ?? false,
      isPower2On: json['isPower2On'] as bool? ?? false,
      isPower1Online: json['isPower1Online'] as bool? ?? true,
      isPower2Online: json['isPower2Online'] as bool? ?? true,
      isFavourite1On: json['isFavourite1On'] as bool? ?? false,
      isFavourite2On: json['isFavourite2On'] as bool? ?? false,
      isFavourite1Online: json['isFavourite1Online'] as bool? ?? true,
      isFavourite2Online: json['isFavourite2Online'] as bool? ?? true,
      isConnected: json['isConnected'] as bool? ?? false,
    );

Map<String, dynamic> _$$DeviceStateImplToJson(_$DeviceStateImpl instance) =>
    <String, dynamic>{
      'isFanOn': instance.isFanOn,
      'fanLevel': instance.fanLevel,
      'fanMode': instance.fanMode,
      'isFanOnline': instance.isFanOnline,
      'isLightOn': instance.isLightOn,
      'brightness': instance.brightness,
      'isLightOnline': instance.isLightOnline,
      'isPower1On': instance.isPower1On,
      'isPower2On': instance.isPower2On,
      'isPower1Online': instance.isPower1Online,
      'isPower2Online': instance.isPower2Online,
      'isFavourite1On': instance.isFavourite1On,
      'isFavourite2On': instance.isFavourite2On,
      'isFavourite1Online': instance.isFavourite1Online,
      'isFavourite2Online': instance.isFavourite2Online,
      'isConnected': instance.isConnected,
    };
