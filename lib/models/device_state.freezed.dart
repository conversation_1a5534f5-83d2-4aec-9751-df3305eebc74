// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'device_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

DeviceState _$DeviceStateFromJson(Map<String, dynamic> json) {
  return _DeviceState.fromJson(json);
}

/// @nodoc
mixin _$DeviceState {
// Fan state
  bool get isFanOn => throw _privateConstructorUsedError;
  int get fanLevel => throw _privateConstructorUsedError;
  String get fanMode => throw _privateConstructorUsedError;
  bool get isFanOnline => throw _privateConstructorUsedError; // Light state
  bool get isLightOn => throw _privateConstructorUsedError;
  double get brightness => throw _privateConstructorUsedError;
  bool get isLightOnline => throw _privateConstructorUsedError; // Power states
  bool get isPower1On => throw _privateConstructorUsedError;
  bool get isPower2On => throw _privateConstructorUsedError;
  bool get isPower1Online => throw _privateConstructorUsedError;
  bool get isPower2Online =>
      throw _privateConstructorUsedError; // Favorite states
  bool get isFavourite1On => throw _privateConstructorUsedError;
  bool get isFavourite2On => throw _privateConstructorUsedError;
  bool get isFavourite1Online => throw _privateConstructorUsedError;
  bool get isFavourite2Online =>
      throw _privateConstructorUsedError; // WebSocket connection state
  bool get isConnected => throw _privateConstructorUsedError;

  /// Serializes this DeviceState to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of DeviceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $DeviceStateCopyWith<DeviceState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $DeviceStateCopyWith<$Res> {
  factory $DeviceStateCopyWith(
          DeviceState value, $Res Function(DeviceState) then) =
      _$DeviceStateCopyWithImpl<$Res, DeviceState>;
  @useResult
  $Res call(
      {bool isFanOn,
      int fanLevel,
      String fanMode,
      bool isFanOnline,
      bool isLightOn,
      double brightness,
      bool isLightOnline,
      bool isPower1On,
      bool isPower2On,
      bool isPower1Online,
      bool isPower2Online,
      bool isFavourite1On,
      bool isFavourite2On,
      bool isFavourite1Online,
      bool isFavourite2Online,
      bool isConnected});
}

/// @nodoc
class _$DeviceStateCopyWithImpl<$Res, $Val extends DeviceState>
    implements $DeviceStateCopyWith<$Res> {
  _$DeviceStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of DeviceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isFanOn = null,
    Object? fanLevel = null,
    Object? fanMode = null,
    Object? isFanOnline = null,
    Object? isLightOn = null,
    Object? brightness = null,
    Object? isLightOnline = null,
    Object? isPower1On = null,
    Object? isPower2On = null,
    Object? isPower1Online = null,
    Object? isPower2Online = null,
    Object? isFavourite1On = null,
    Object? isFavourite2On = null,
    Object? isFavourite1Online = null,
    Object? isFavourite2Online = null,
    Object? isConnected = null,
  }) {
    return _then(_value.copyWith(
      isFanOn: null == isFanOn
          ? _value.isFanOn
          : isFanOn // ignore: cast_nullable_to_non_nullable
              as bool,
      fanLevel: null == fanLevel
          ? _value.fanLevel
          : fanLevel // ignore: cast_nullable_to_non_nullable
              as int,
      fanMode: null == fanMode
          ? _value.fanMode
          : fanMode // ignore: cast_nullable_to_non_nullable
              as String,
      isFanOnline: null == isFanOnline
          ? _value.isFanOnline
          : isFanOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      isLightOn: null == isLightOn
          ? _value.isLightOn
          : isLightOn // ignore: cast_nullable_to_non_nullable
              as bool,
      brightness: null == brightness
          ? _value.brightness
          : brightness // ignore: cast_nullable_to_non_nullable
              as double,
      isLightOnline: null == isLightOnline
          ? _value.isLightOnline
          : isLightOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower1On: null == isPower1On
          ? _value.isPower1On
          : isPower1On // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower2On: null == isPower2On
          ? _value.isPower2On
          : isPower2On // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower1Online: null == isPower1Online
          ? _value.isPower1Online
          : isPower1Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower2Online: null == isPower2Online
          ? _value.isPower2Online
          : isPower2Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite1On: null == isFavourite1On
          ? _value.isFavourite1On
          : isFavourite1On // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite2On: null == isFavourite2On
          ? _value.isFavourite2On
          : isFavourite2On // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite1Online: null == isFavourite1Online
          ? _value.isFavourite1Online
          : isFavourite1Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite2Online: null == isFavourite2Online
          ? _value.isFavourite2Online
          : isFavourite2Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$DeviceStateImplCopyWith<$Res>
    implements $DeviceStateCopyWith<$Res> {
  factory _$$DeviceStateImplCopyWith(
          _$DeviceStateImpl value, $Res Function(_$DeviceStateImpl) then) =
      __$$DeviceStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool isFanOn,
      int fanLevel,
      String fanMode,
      bool isFanOnline,
      bool isLightOn,
      double brightness,
      bool isLightOnline,
      bool isPower1On,
      bool isPower2On,
      bool isPower1Online,
      bool isPower2Online,
      bool isFavourite1On,
      bool isFavourite2On,
      bool isFavourite1Online,
      bool isFavourite2Online,
      bool isConnected});
}

/// @nodoc
class __$$DeviceStateImplCopyWithImpl<$Res>
    extends _$DeviceStateCopyWithImpl<$Res, _$DeviceStateImpl>
    implements _$$DeviceStateImplCopyWith<$Res> {
  __$$DeviceStateImplCopyWithImpl(
      _$DeviceStateImpl _value, $Res Function(_$DeviceStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of DeviceState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? isFanOn = null,
    Object? fanLevel = null,
    Object? fanMode = null,
    Object? isFanOnline = null,
    Object? isLightOn = null,
    Object? brightness = null,
    Object? isLightOnline = null,
    Object? isPower1On = null,
    Object? isPower2On = null,
    Object? isPower1Online = null,
    Object? isPower2Online = null,
    Object? isFavourite1On = null,
    Object? isFavourite2On = null,
    Object? isFavourite1Online = null,
    Object? isFavourite2Online = null,
    Object? isConnected = null,
  }) {
    return _then(_$DeviceStateImpl(
      isFanOn: null == isFanOn
          ? _value.isFanOn
          : isFanOn // ignore: cast_nullable_to_non_nullable
              as bool,
      fanLevel: null == fanLevel
          ? _value.fanLevel
          : fanLevel // ignore: cast_nullable_to_non_nullable
              as int,
      fanMode: null == fanMode
          ? _value.fanMode
          : fanMode // ignore: cast_nullable_to_non_nullable
              as String,
      isFanOnline: null == isFanOnline
          ? _value.isFanOnline
          : isFanOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      isLightOn: null == isLightOn
          ? _value.isLightOn
          : isLightOn // ignore: cast_nullable_to_non_nullable
              as bool,
      brightness: null == brightness
          ? _value.brightness
          : brightness // ignore: cast_nullable_to_non_nullable
              as double,
      isLightOnline: null == isLightOnline
          ? _value.isLightOnline
          : isLightOnline // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower1On: null == isPower1On
          ? _value.isPower1On
          : isPower1On // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower2On: null == isPower2On
          ? _value.isPower2On
          : isPower2On // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower1Online: null == isPower1Online
          ? _value.isPower1Online
          : isPower1Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isPower2Online: null == isPower2Online
          ? _value.isPower2Online
          : isPower2Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite1On: null == isFavourite1On
          ? _value.isFavourite1On
          : isFavourite1On // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite2On: null == isFavourite2On
          ? _value.isFavourite2On
          : isFavourite2On // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite1Online: null == isFavourite1Online
          ? _value.isFavourite1Online
          : isFavourite1Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isFavourite2Online: null == isFavourite2Online
          ? _value.isFavourite2Online
          : isFavourite2Online // ignore: cast_nullable_to_non_nullable
              as bool,
      isConnected: null == isConnected
          ? _value.isConnected
          : isConnected // ignore: cast_nullable_to_non_nullable
              as bool,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$DeviceStateImpl implements _DeviceState {
  const _$DeviceStateImpl(
      {this.isFanOn = false,
      this.fanLevel = 1,
      this.fanMode = 'Normal',
      this.isFanOnline = true,
      this.isLightOn = false,
      this.brightness = 0.5,
      this.isLightOnline = true,
      this.isPower1On = false,
      this.isPower2On = false,
      this.isPower1Online = true,
      this.isPower2Online = true,
      this.isFavourite1On = false,
      this.isFavourite2On = false,
      this.isFavourite1Online = true,
      this.isFavourite2Online = true,
      this.isConnected = false});

  factory _$DeviceStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$DeviceStateImplFromJson(json);

// Fan state
  @override
  @JsonKey()
  final bool isFanOn;
  @override
  @JsonKey()
  final int fanLevel;
  @override
  @JsonKey()
  final String fanMode;
  @override
  @JsonKey()
  final bool isFanOnline;
// Light state
  @override
  @JsonKey()
  final bool isLightOn;
  @override
  @JsonKey()
  final double brightness;
  @override
  @JsonKey()
  final bool isLightOnline;
// Power states
  @override
  @JsonKey()
  final bool isPower1On;
  @override
  @JsonKey()
  final bool isPower2On;
  @override
  @JsonKey()
  final bool isPower1Online;
  @override
  @JsonKey()
  final bool isPower2Online;
// Favorite states
  @override
  @JsonKey()
  final bool isFavourite1On;
  @override
  @JsonKey()
  final bool isFavourite2On;
  @override
  @JsonKey()
  final bool isFavourite1Online;
  @override
  @JsonKey()
  final bool isFavourite2Online;
// WebSocket connection state
  @override
  @JsonKey()
  final bool isConnected;

  @override
  String toString() {
    return 'DeviceState(isFanOn: $isFanOn, fanLevel: $fanLevel, fanMode: $fanMode, isFanOnline: $isFanOnline, isLightOn: $isLightOn, brightness: $brightness, isLightOnline: $isLightOnline, isPower1On: $isPower1On, isPower2On: $isPower2On, isPower1Online: $isPower1Online, isPower2Online: $isPower2Online, isFavourite1On: $isFavourite1On, isFavourite2On: $isFavourite2On, isFavourite1Online: $isFavourite1Online, isFavourite2Online: $isFavourite2Online, isConnected: $isConnected)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeviceStateImpl &&
            (identical(other.isFanOn, isFanOn) || other.isFanOn == isFanOn) &&
            (identical(other.fanLevel, fanLevel) ||
                other.fanLevel == fanLevel) &&
            (identical(other.fanMode, fanMode) || other.fanMode == fanMode) &&
            (identical(other.isFanOnline, isFanOnline) ||
                other.isFanOnline == isFanOnline) &&
            (identical(other.isLightOn, isLightOn) ||
                other.isLightOn == isLightOn) &&
            (identical(other.brightness, brightness) ||
                other.brightness == brightness) &&
            (identical(other.isLightOnline, isLightOnline) ||
                other.isLightOnline == isLightOnline) &&
            (identical(other.isPower1On, isPower1On) ||
                other.isPower1On == isPower1On) &&
            (identical(other.isPower2On, isPower2On) ||
                other.isPower2On == isPower2On) &&
            (identical(other.isPower1Online, isPower1Online) ||
                other.isPower1Online == isPower1Online) &&
            (identical(other.isPower2Online, isPower2Online) ||
                other.isPower2Online == isPower2Online) &&
            (identical(other.isFavourite1On, isFavourite1On) ||
                other.isFavourite1On == isFavourite1On) &&
            (identical(other.isFavourite2On, isFavourite2On) ||
                other.isFavourite2On == isFavourite2On) &&
            (identical(other.isFavourite1Online, isFavourite1Online) ||
                other.isFavourite1Online == isFavourite1Online) &&
            (identical(other.isFavourite2Online, isFavourite2Online) ||
                other.isFavourite2Online == isFavourite2Online) &&
            (identical(other.isConnected, isConnected) ||
                other.isConnected == isConnected));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      isFanOn,
      fanLevel,
      fanMode,
      isFanOnline,
      isLightOn,
      brightness,
      isLightOnline,
      isPower1On,
      isPower2On,
      isPower1Online,
      isPower2Online,
      isFavourite1On,
      isFavourite2On,
      isFavourite1Online,
      isFavourite2Online,
      isConnected);

  /// Create a copy of DeviceState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeviceStateImplCopyWith<_$DeviceStateImpl> get copyWith =>
      __$$DeviceStateImplCopyWithImpl<_$DeviceStateImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$DeviceStateImplToJson(
      this,
    );
  }
}

abstract class _DeviceState implements DeviceState {
  const factory _DeviceState(
      {final bool isFanOn,
      final int fanLevel,
      final String fanMode,
      final bool isFanOnline,
      final bool isLightOn,
      final double brightness,
      final bool isLightOnline,
      final bool isPower1On,
      final bool isPower2On,
      final bool isPower1Online,
      final bool isPower2Online,
      final bool isFavourite1On,
      final bool isFavourite2On,
      final bool isFavourite1Online,
      final bool isFavourite2Online,
      final bool isConnected}) = _$DeviceStateImpl;

  factory _DeviceState.fromJson(Map<String, dynamic> json) =
      _$DeviceStateImpl.fromJson;

// Fan state
  @override
  bool get isFanOn;
  @override
  int get fanLevel;
  @override
  String get fanMode;
  @override
  bool get isFanOnline; // Light state
  @override
  bool get isLightOn;
  @override
  double get brightness;
  @override
  bool get isLightOnline; // Power states
  @override
  bool get isPower1On;
  @override
  bool get isPower2On;
  @override
  bool get isPower1Online;
  @override
  bool get isPower2Online; // Favorite states
  @override
  bool get isFavourite1On;
  @override
  bool get isFavourite2On;
  @override
  bool get isFavourite1Online;
  @override
  bool get isFavourite2Online; // WebSocket connection state
  @override
  bool get isConnected;

  /// Create a copy of DeviceState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeviceStateImplCopyWith<_$DeviceStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
