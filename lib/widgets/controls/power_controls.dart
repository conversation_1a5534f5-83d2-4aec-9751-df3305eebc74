import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';
import '../../models/device_type.dart';

class PowerControls extends ConsumerWidget {
  final double maxWidth;
  final bool isPowerOn;
  final bool isSecondPower;

  const PowerControls({
    super.key,
    required this.maxWidth,
    required this.isPowerOn,
    this.isSecondPower = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final notifier = ref.read(deviceStateNotifierProvider.notifier);
    
    // Adjust sizes based on available width
    final titleSize = maxWidth < 400 ? 16.0 : 18.0;
    final padding = maxWidth < 400 ? 8.0 : 12.0;

    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(12),
      ),
      child: SingleChildScrollView(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Power Control',
              style: TextStyle(fontSize: titleSize, fontWeight: FontWeight.bold),
            ),
            Switch(
              value: isPowerOn,
              onChanged: (_) {
                if (isSecondPower) {
                  notifier.togglePower2();
                } else {
                  notifier.togglePower1();
                }
              },
              activeColor: Colors.blue,
            ),
          ],
        ),
      ),
    );
  }
}
