import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../providers/device_state_provider.dart';
import '../painters/fan_speed_arc_painter.dart';

class FanCircularWidget extends ConsumerWidget {
  final double arcSize;
  final double centerCircleSize;
  final double fontSize;

  const FanCircularWidget({
    super.key,
    required this.arcSize,
    required this.centerCircleSize,
    required this.fontSize,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final deviceState = ref.watch(deviceStateNotifierProvider);
    final notifier = ref.read(deviceStateNotifierProvider.notifier);
    
    // Normalize fan level to 0.0-1.0 for the painter
    final normalizedLevel = deviceState.fanLevel / 4;

    return Stack(
      alignment: Alignment.center,
      children: [
        // Fan speed arc
        CustomPaint(
          size: Size(arcSize, arcSize),
          painter: FanSpeedArcPainter(normalizedLevel),
        ),

        // Center fan level display
        Container(
          width: centerCircleSize,
          height: centerCircleSize,
          decoration: BoxDecoration(
            color: deviceState.isFanOn ? Colors.blue : Colors.grey[700],
            shape: BoxShape.circle,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                deviceState.isFanOn ? '${deviceState.fanLevel}' : 'OFF',
                style: TextStyle(
                  fontSize: fontSize,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
