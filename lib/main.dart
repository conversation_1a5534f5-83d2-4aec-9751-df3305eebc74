import 'dart:developer';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'screens/virtual_dock_screen.dart';
import 'services/analytics_service.dart';
import 'services/connectivity_service.dart';
import 'services/performance_service.dart';
import 'services/websocket_monitor_service.dart';
import 'providers/home_assistant_provider.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // Initialize Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    // Initialize Firebase Crashlytics
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

    // Enable Firebase Performance Monitoring
    await FirebasePerformance.instance.setPerformanceCollectionEnabled(true);

    // Initialize analytics service
    final analyticsService = AnalyticsService();
    await analyticsService.initialize();

    // Initialize performance service
    final performanceService = PerformanceService();
    await performanceService.initialize();

    // Initialize WebSocket monitor service
    final websocketMonitorService = WebSocketMonitorService();
    await websocketMonitorService.initialize();

    // Initialize connectivity service
    final connectivityService = ConnectivityService();
    await connectivityService.initialize();

    // Log app start
    analyticsService.trackAppSession(isStart: true);

    log('App initialized with Firebase and services');
  } catch (e) {
    log('Error initializing app: $e');
    // Still try to record the error even if initialization failed
    FirebaseCrashlytics.instance.recordError(e, StackTrace.current);
  }

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'Schnell Smart Home',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      darkTheme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue,
          brightness: Brightness.dark,
        ),
        useMaterial3: true,
      ),
      themeMode: ThemeMode.dark,
      home: const VirtualDockScreen(),
    );
  }
}
